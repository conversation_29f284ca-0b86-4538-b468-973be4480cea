import { Box, Tooltip, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import CustomSelect from '@/components/UI/CustomSelect';
import {
  checkOrganizationRole,
  DateFormat,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { identifiers } from '@/helper/constants/identifier';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import { supportTicketService } from '@/services/supportTicketService';
import { staticOptions } from '@/helper/common/staticOptions';
import './ticket.scss';

const statusOptions = staticOptions.SUPPORT_TICKET_STATUS_OPTIONS;

export default function Ticket({
  onTicketClick,
  ticketsList = [], // Accept list of tickets
  selectedTicket, // Track which ticket is selected
  hideDropdown = false, // New prop to conditionally hide dropdown
  hideStatusChip = false, // New prop to conditionally hide status chip
  showBottomPadding = false, // New prop to conditionally show pb8 class
  onTicketStatusChange, // New prop to handle individual ticket status changes
  onTicketDelete, // New prop to handle ticket deletion
  onStatusFilterChange, // New prop to handle status filter API calls
  hideStatusFilter = false, // New prop to conditionally hide status filter
  isLoading = false, // New prop to show loading state
}) {
  const router = useRouter();
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // State for status filter - load from localStorage
  const [statusFilter, setStatusFilter] = useState(() => {
    const savedFilter = fetchFromStorage(
      identifiers?.SUPPORT_TICKET_STATUS_FILTER
    );
    return savedFilter || '';
  });

  // Refs for auto-scroll functionality
  const ticketRefs = useRef({});

  // Auto-scroll to selected ticket when selectedTicket changes
  useEffect(() => {
    if (selectedTicket?.id) {
      // Add a delay to ensure DOM is fully rendered, especially after mobile view changes
      const scrollTimeout = setTimeout(() => {
        const selectedElement = ticketRefs.current[selectedTicket.id];

        if (selectedElement) {
          // Find the scrollable parent container (.tickets-list-wrapper)
          const container = selectedElement.closest('.tickets-list-wrapper');

          if (container) {
            // Calculate the position to scroll to
            const elementTop = selectedElement.offsetTop;
            const elementHeight = selectedElement.offsetHeight;
            const containerHeight = container.clientHeight;

            // Always scroll to the selected ticket to ensure it's visible and centered
            // This ensures consistent behavior when returning from detail view
            const scrollPosition =
              elementTop - containerHeight / 2 + elementHeight / 2;

            // Scroll to the selected ticket
            container.scrollTo({
              top: Math.max(0, scrollPosition),
              behavior: 'smooth',
            });

            // Add a small additional delay to ensure the element is properly highlighted
            setTimeout(() => {
              // Force a re-render to ensure the selected class is applied
              const ticketElement = document.querySelector(
                `[data-ticket-id="${selectedTicket.id}"]`
              );
              if (ticketElement) {
                ticketElement.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center',
                  inline: 'nearest',
                });
              }
            }, 100);
          }
        }
      }, 300); // Increased delay to ensure proper DOM rendering after mobile view changes

      return () => clearTimeout(scrollTimeout);
    }
  }, [selectedTicket?.id, ticketsList]);

  // Apply saved filter on component mount
  useEffect(() => {
    if (statusFilter && onStatusFilterChange) {
      // Apply the saved filter by calling the API
      onStatusFilterChange(statusFilter);
    }
  }, []); // Empty dependency array to run only on mount

  const isStaff = checkOrganizationRole('staff');
  const isSuperAdmin = checkOrganizationRole('super_admin');

  // Function to get priority CSS class
  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed', // Red - Most critical
      high: 'high', // Orange/Yellow - High priority but less than urgent
      medium: 'ongoing', // Blue - Medium priority
      low: 'draft', // Green - Low priority
      none: 'draft', // Blue - Default
    };
    return priorityMap[priority?.toLowerCase()] || 'draft';
  };

  // Function to format priority display consistently
  const formatPriorityDisplay = (priority) => {
    if (!priority) return '';
    // Convert to lowercase first, then capitalize first letter
    const formatted = priority.toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  };

  // Function to get status CSS class
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'draft',
      escalated: 'failed',
      in_progress: 'ongoing',
      on_hold: 'ongoing',
      qa_review: 'probation',
      assigned: 'probation',
      under_review: 'probation',
      resolved: 'success',
      closed: 'status-verified',
    };
    return statusMap[status] || 'draft';
  };

  // Use only provided tickets list - no default fallback to avoid duplication
  const ticketsToDisplay = ticketsList;

  // Status filter options for dropdown
  const statusFilterOptions = [
    { label: 'All Status', value: '' },
    ...staticOptions.SUPPORT_TICKET_STATUS_OPTIONS,
  ];

  const handleTicketClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onTicketClick) {
      onTicketClick(ticket);
    }
  };

  const handleStatusChange = async (ticket, newStatus) => {
    try {
      // Call API to update ticket status
      const updateData = {
        ticket_status: newStatus,
      };

      const result = await supportTicketService.updateTicket(
        ticket?.id,
        updateData
      );

      if (result) {
        // Show success message
        setApiMessage(
          'success',
          result?.message || 'Status updated successfully'
        );

        // Call the parent's status change handler if provided
        if (onTicketStatusChange) {
          onTicketStatusChange(ticket?.id, newStatus);
        }
      }
    } catch (error) {
      // Show error message
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'Failed to update status. Please try again.'
      );
    }
  };

  const handleDeleteClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Show confirmation dialog instead of direct delete
    setTicketToDelete(ticket);
    setDeleteDialogOpen(true);
  };

  // Add the handler function for edit
  const handleEditClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Navigate to edit ticket page with ticket ID
    router.push(`/support-ticket/edit-ticket/${ticket?.id}`);
  };

  // Handle cancel delete dialog
  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setTicketToDelete(null);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!ticketToDelete) return;

    try {
      setIsDeleting(true);

      // Call API to delete ticket
      const result = await supportTicketService.deleteTicket(
        ticketToDelete?.id
      );
      // Show success message
      setApiMessage('success', result?.message);

      // Call parent's delete handler if provided
      if (onTicketDelete) {
        await onTicketDelete(ticketToDelete);
      }

      // Close dialog
      setDeleteDialogOpen(false);
      setTicketToDelete(null);
    } catch (error) {
      // Show error message to user
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'Failed to delete ticket. Please try again.'
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Box className="tickets-list-container ticket-list-container-wrap">
      {/* Status Filter Dropdown - Always show when not hidden */}
      {!hideStatusFilter && (
        <Box className="status-filter-container mb16">
          <CustomSelect
            className="status-filter-select"
            placeholder="Filter by Status"
            options={statusFilterOptions}
            value={
              statusFilter
                ? statusFilterOptions.find(
                    (option) => option.value === statusFilter
                  )
                : statusFilterOptions[0] // Default to "All Status"
            }
            onChange={(selectedOption) => {
              const newStatus = selectedOption?.value || '';
              setStatusFilter(newStatus);

              // Save filter to localStorage for persistence
              saveToStorage(
                identifiers?.SUPPORT_TICKET_STATUS_FILTER,
                newStatus
              );

              // Call API if onStatusFilterChange prop is provided
              if (onStatusFilterChange) {
                onStatusFilterChange(newStatus);
              }
            }}
            menuPortalTarget={document?.body}
            styles={{
              control: (provided) => ({
                ...provided,
                minWidth: '200px',
                fontSize: '14px',
              }),
            }}
          />
        </Box>
      )}
      {/* Show Loader, No Data View, or Tickets List */}
      {isLoading ? (
        <ContentLoader />
      ) : !ticketsToDisplay || ticketsToDisplay.length === 0 ? (
        <Box className="no-tickets-container">
          <NoDataView
            title="No Support Tickets Found"
            description="No tickets match the current filter criteria. Try adjusting your filters or create a new ticket."
            className="no-data-auto-margin-height-conainer"
          />
        </Box>
      ) : (
        /* Tickets List */
        <Box className="cursor-pointer">
          {ticketsToDisplay?.map((ticketData, index) => {
            return (
              <Box
                key={ticketData?.id || index}
                data-ticket-id={ticketData?.id}
                ref={(el) => {
                  if (el && ticketData?.id) {
                    ticketRefs.current[ticketData.id] = el;
                  }
                }}
                className={`ticket-wrap ${selectedTicket?.id === ticketData?.id ? 'selected' : ''}`}
                onClick={handleTicketClick(ticketData)}
              >
                <Box className="heading-wrap">
                  {!hideStatusChip && ticketData?.ticket_status && (
                    <Box className="status-delete-wrap d-flex align-center gap-5 justify-space-between mb8">
                      <Typography
                        className={`sub-title-text fw600 status-chip-wrap ${getStatusClass(ticketData?.ticket_status)}`}
                      >
                        {ticketData?.ticket_status?.replace('_', ' ')}
                      </Typography>
                      <Box className="d-flex align-center gap-5">
                        {isSuperAdmin && (
                          <EditIcon onClick={handleEditClick(ticketData)} />
                        )}
                        {isSuperAdmin && (
                          <DeleteIcon onClick={handleDeleteClick(ticketData)} />
                        )}
                      </Box>
                    </Box>
                  )}
                  <Typography className="heading-text-wrap body-sm d-flex align-center gap-5">
                    <span>#{ticketData?.id || '-'}</span>{' '}
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          {ticketData?.ticket_title || '-'}
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <span className="text-ellipsis-line">
                        {ticketData?.ticket_title || '-'}
                      </span>
                    </Tooltip>
                  </Typography>
                </Box>

                <Box className="description-wrap">
                  {ticketData?.ticket_description ? (
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          {ticketData?.ticket_description}
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <Typography className="body-sm text-ellipsis description-text">
                        {ticketData?.ticket_description}
                      </Typography>
                    </Tooltip>
                  ) : (
                    <Typography className="description-text body-sm">
                      -
                    </Typography>
                  )}
                </Box>

                <Box className="name-time-wrap pb4">
                  <Box
                    className={`d-flex align-center gap-5 pb4 ${showBottomPadding ? 'pb8' : ''}`}
                  >
                    <PersonIcon className="user-icon" />
                    <Typography className="name-text body-sm">
                      {ticketData?.ticket_owner_name || '-'}
                    </Typography>
                  </Box>
                  <Box className="d-flex align-center justify-space-between gap-5 flex-wrap">
                    <Box className="d-flex align-center gap-5">
                      <AccessTimeIcon className="time-icon" />
                      <Typography className="time-text body-sm">
                        {ticketData?.created_at
                          ? DateFormat(ticketData?.created_at, 'datesWithhour')
                          : '-'}
                      </Typography>
                    </Box>
                    {/* Show priority next to date */}
                    {ticketData?.ticket_priority && (
                      <Typography
                        className={`sub-title-text fw600 ${getPriorityClass(ticketData?.ticket_priority)}`}
                      >
                        {formatPriorityDisplay(ticketData?.ticket_priority)}
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* {ticketData?.assignedTo && (
            <Box className="assigned-wrap">
              <p className="assigned-text body-sm">
                <span className="assigned-label">Assigned to:</span>{' '}
                {ticketData?.assignedTo}
              </p>
            </Box>
          )} */}

                {!hideDropdown && (
                  <Box
                    className="ticket-status-select-container d-flex align-center gap-5 pt8"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent ticket click when clicking on select
                    }}
                  >
                    {(isSuperAdmin || isStaff) && (
                      <CustomSelect
                        className="slected-wrap ticket-status-select"
                        placeholder="Select Status"
                        options={statusOptions}
                        value={
                          statusOptions?.find(
                            (opt) =>
                              opt?.value === ticketData?.ticket_status ||
                              opt?.value ===
                                ticketData?.ticket_status?.toLowerCase()
                          ) || ''
                        }
                        name="status"
                        onChange={(e) => {
                          // If cross icon is clicked (e is null), set status to "open"
                          const newStatus = e?.value || 'open';
                          handleStatusChange(ticketData, newStatus);
                        }}
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    )}
                    <Box className="d-flex align-center gap-5">
                      {isSuperAdmin && (
                        <EditIcon onClick={handleEditClick(ticketData)} />
                      )}
                      {isSuperAdmin && (
                        <DeleteIcon onClick={handleDeleteClick(ticketData)} />
                      )}
                    </Box>
                  </Box>
                )}

                {/* <Box className="profile-wrap">
            <Image
              className="profile-image"
              src={ProfileImage}
              alt="Profile Image"
              width={100}
              height={100}
            />
          </Box> */}
              </Box>
            );
          })}
        </Box>
      )}
      {/* Delete Confirmation Dialog */}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCancelDelete}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCancelDelete}
            handleConfirm={handleConfirmDelete}
            text={`Are you sure you want to delete "${ticketToDelete?.ticket_title || 'this ticket'}"?`}
            confirmText={isDeleting ? 'Deleting...' : 'Yes, Delete it'}
            cancelText="Cancel"
          />
        }
      />
    </Box>
  );
}
