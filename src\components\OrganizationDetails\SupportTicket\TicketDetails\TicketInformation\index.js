'use client';
import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomUserSelect from '@/components/UI/CustomUserSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import HeaderImage from '@/components/UI/ImageSecurity';
import { Box, Tooltip, Typography, Avatar } from '@mui/material';
import dayjs from 'dayjs';
import moment from 'moment';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import TicketSummaryCard from './TicketSummaryCard';
import { fetchFromStorage, removeFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { supportTicketService } from '@/services/supportTicketService';
import { staticOptions } from '@/helper/common/staticOptions';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './ticketinformation.scss';

// Dynamic validation schema that handles conditional validation properly
const createValidationSchema = (isSuperAdmin) => {
  return Yup.object().shape({
    description: Yup.string().required('Description is required'),
    status: Yup.string()
      .required('Status is required')
      .test(
        'open-status-validation',
        'Cannot add start date, due date, or estimated hours when status is "Open". Please change status to "In Progress" or another status first.',
        function (value) {
          if (value === 'open') {
            const { sdate, dueDate, estimated_hours } = this.parent;
            return !sdate && !dueDate && !estimated_hours;
          }
          return true;
        }
      ),
    priority: Yup.string().required('Priority is required'),
    classification: Yup.string().required('Classification is required'),

    // Assignee validation for super_admin
    assignee: isSuperAdmin
      ? Yup.mixed().required('Assign ticket first')
      : Yup.mixed().nullable().notRequired(),

    // Conditional validation for date and hours fields
    sdate: Yup.mixed().when(['status', 'assignee'], {
      is: (status, assignee) => {
        // Make optional if super_admin with open status OR assigned with open status
        const isSuperAdminAndOpen = isSuperAdmin && status === 'open';
        const isAssignedAndOpen = assignee && status === 'open';
        return isSuperAdminAndOpen || isAssignedAndOpen;
      },
      then: (schema) => schema.nullable().notRequired(),
      otherwise: (schema) => schema.required('Start Date is required'),
    }),

    dueDate: Yup.mixed().when(['status', 'assignee', 'sdate'], {
      is: (status, assignee) => {
        // Make optional if super_admin with open status OR assigned with open status
        const isSuperAdminAndOpen = isSuperAdmin && status === 'open';
        const isAssignedAndOpen = assignee && status === 'open';
        return isSuperAdminAndOpen || isAssignedAndOpen;
      },
      then: (schema) => schema.nullable().notRequired(),
      otherwise: (schema) =>
        schema
          .required('Due Date is required')
          .test(
            'is-after-start',
            'Due date must be after or equal to start date',
            function (value) {
              const { sdate } = this.parent;
              if (!value || !sdate) return true;
              return moment(value).isSameOrAfter(moment(sdate), 'day');
            }
          ),
    }),

    estimated_hours: Yup.mixed().when(['status', 'assignee'], {
      is: (status, assignee) => {
        // Make optional if super_admin with open status OR assigned with open status
        const isSuperAdminAndOpen = isSuperAdmin && status === 'open';
        const isAssignedAndOpen = assignee && status === 'open';
        return isSuperAdminAndOpen || isAssignedAndOpen;
      },
      then: (schema) => schema.nullable().notRequired(),
      otherwise: (schema) =>
        schema
          .required('Estimated hours is required')
          .test('is-number', 'Must be a valid number', (value) => {
            if (!value) return false;
            const num = Number(value);
            return !isNaN(num) && num >= 0 && num <= 9999;
          }),
    }),
  });
};

export default function TicketInformation({ ticket, onTicketStatusChange }) {
  const router = useRouter();
  const [taskFollowers, setTaskFollowers] = useState([]);
  const [isAddFollowerOpen, setIsAddFollowerOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [orgUsers, setOrgUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Get current user
  const user = fetchFromStorage(identifiers.USER_DATA);

  // Get user role to determine access
  const isStaff = checkOrganizationRole('staff');
  const isSuperAdmin = checkOrganizationRole('super_admin');
  const isOrgMaster = checkOrganizationRole('org_master');

  // Fetch organization users for assignee dropdown and followers
  const fetchOrgUsers = async () => {
    // Only call API if user has super admin role
    if (!isSuperAdmin) {
      setLoadingUsers(false);
      setOrgUsers([]);
      return;
    }

    try {
      setLoadingUsers(true);
      const response = await supportTicketService.getOrgUserList();
      if (response && response.users) {
        setOrgUsers(response.users);
      }
      setApiMessage('success', response?.message);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setOrgUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Simulate loading for consistency with other tabs
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500); // Brief loading to match other tabs

    // Fetch organization users when component mounts
    fetchOrgUsers();

    // Initialize followers from ticket data
    if (ticket?.followers && Array.isArray(ticket.followers)) {
      // Transform API followers to match UI format
      const transformedFollowers = ticket?.followers?.map((follower) => ({
        name: follower?.full_name || follower?.name || 'Unknown User',
        email: follower?.email || '',
        id: follower?.id || follower?.user_id,
        image: follower?.avatar || follower?.profile_image,
      }));
      setTaskFollowers(transformedFollowers);
    }

    return () => clearTimeout(timer);
  }, [ticket]);

  // Get display name for ticket owner
  const getTicketOwnerDisplayName = () => {
    if (isSuperAdmin) {
      // For super_admin, show the actual name from API response
      return ticket?.creator_full_name;
    } else if (isOrgMaster) {
      // For org_master, show "You"
      return 'You';
    } else {
      // For other roles, show the actual name
      return ticket?.creator_full_name;
    }
  };

  // Use API users for both assignee and followers
  const assigneeOptions = orgUsers || [];

  // Determine if user can edit (only super_admin can edit)
  const canEdit = isSuperAdmin;

  // Determine if user can edit form fields (super_admin and org_master can edit fields)
  const canEditFields = isSuperAdmin || isOrgMaster;

  // Status options - matching the Ticket component options
  const statusOptions = staticOptions.SUPPORT_TICKET_STATUS_OPTIONS;

  const priorityOptions = staticOptions.SUPPORT_TICKET_PRIORITY_OPTIONS;

  const classificationOptions = staticOptions.SUPPORT_TICKET_MODULE_OPTIONS;

  // Helper function to get display value for options
  const getDisplayValue = (value, options) => {
    const option = options?.find((opt) => opt.value === value);
    return option?.label || value || 'Not specified';
  };

  // Add follower to the task
  const addFollower = (follower) => {
    if (!canEdit) return; // Only allow editing if user has permission
    setTaskFollowers([...taskFollowers, follower]);
  };

  // Remove a follower from the task
  const removeFollowers = (index) => {
    if (!canEdit) return; // Only allow editing if user has permission
    const existingFollowers = taskFollowers?.filter((_, i) => i !== index);
    setTaskFollowers(existingFollowers);
  };

  // Get available followers from API users (those who are not yet added and not the assignee)
  // This function will be called inside Formik to access current assignee value
  const getAvailableFollowers = (currentAssignee) => {
    return (
      orgUsers?.filter(
        (user) =>
          // Exclude users already in taskFollowers
          !taskFollowers?.some(
            (taskFollower) => taskFollower?.name === user?.label
          ) &&
          // Exclude currently selected assignee
          (!currentAssignee || user?.value !== currentAssignee?.value)
      ) || []
    );
  };

  const handleToggleClick = () => {
    if (!canEdit) return; // Only allow editing if user has permission
    setIsAddFollowerOpen((prevState) => !prevState);
  };

  const handleCancelClick = () => {
    // Check if there's saved filter data from AllTicketsList
    const redirectData = fetchFromStorage(identifiers?.RedirectData);

    if (redirectData && redirectData?.IsFromUser) {
      // Clear the redirect data since we're going back
      removeFromStorage(identifiers?.RedirectData);
      // Navigate back to all tickets list (filters will be restored automatically)
      router.push('/support-ticket/all-tickets');
    } else {
      // No saved data, navigate normally
      router.push('/support-ticket/all-tickets');
    }
  };

  return (
    <Formik
      key={`${ticket?.id}-${ticket?.ticket_status}-${ticket?.ticket_priority}`}
      initialValues={{
        description: ticket?.ticket_description || '',
        sdate: ticket?.manual_start_date ? ticket?.manual_start_date : null,
        dueDate: ticket?.manual_due_date ? ticket?.manual_due_date : null,
        status: ticket?.ticket_status || '',
        priority: ticket?.ticket_priority || 'low',
        classification: ticket?.ticket_module || '',
        estimated_hours: ticket?.estimated_hours || '',
        assignee: ticket?.assigned_to_user_id
          ? {
              value: ticket?.assigned_to_user_id,
              label: ticket?.assigned_full_name || '',
              email: ticket?.assigned_email || '',
              avatar: ticket?.assigned_avatar_url || '',
            }
          : null,
      }}
      validationSchema={createValidationSchema(isSuperAdmin)}
      validateOnChange={false}
      validateOnBlur={false}
      onSubmit={async (values, { setSubmitting, setErrors }) => {
        // Early validation for super_admin assignee requirement
        if (isSuperAdmin) {
          const hasAssignee = values?.assignee && values?.assignee?.value;
          if (!hasAssignee) {
            setErrors({
              assignee: 'Assign ticket first',
            });
            setSubmitting(false);
            return;
          }
        }

        // Check if user can submit - super_admin, org_master, or assigned user
        const isAssignedUser =
          values?.assignee?.value === user?.id ||
          ticket?.assigned_to_user_id === user?.id;
        const canSubmit = canEditFields || isAssignedUser;

        if (!canSubmit) return;

        // Check if this is super_admin with open status - should allow submission
        const isSuperAdminAndOpen = isSuperAdmin && values?.status === 'open';
        const isAssignedAndOpen =
          (values?.assignee || ticket?.assigned_to_user_id) &&
          values?.status === 'open';
        const shouldMakeOptional = isSuperAdminAndOpen || isAssignedAndOpen;

        // If fields should be optional, clear any validation errors for optional fields
        if (shouldMakeOptional) {
          setErrors({});
        }

        try {
          setSubmitting(true);
          // Prepare followers data for API - only send IDs as numbers
          const followersData =
            taskFollowers?.map((follower) => Number(follower?.id)) || [];

          const payload = {
            ticket_title: ticket?.ticket_title, // Title is not editable in this form, so use existing
            ticket_description: values?.description,
            ticket_module: values?.classification,
            ticket_type: ticket?.ticket_type, // Not editable in this form, so use existing
            ticket_priority: values?.priority,
            ticket_status: values?.status,
            assigned_to_user_id: values?.assignee?.value || null,
            manual_start_date: values?.sdate
              ? dayjs(values.sdate).format('YYYY-MM-DD')
              : null,
            manual_due_date: values?.dueDate
              ? dayjs(values.dueDate).format('YYYY-MM-DD')
              : null,
            estimated_hours: values?.estimated_hours
              ? parseFloat(values.estimated_hours)
              : null,
            followers: followersData,
          };
          const response = await supportTicketService.updateTicket(
            ticket?.id,
            payload
          );
          setApiMessage('success', response?.message);

          // Notify parent component about status change to update the list
          if (
            onTicketStatusChange &&
            values?.status !== ticket?.ticket_status
          ) {
            onTicketStatusChange(ticket?.id, values?.status);
          }

          // Optionally, refresh the page or ticket details here
          router.refresh && router.refresh();
        } catch (error) {
          setApiMessage('error', error?.response?.data?.message);
        } finally {
          setSubmitting(false);
        }
      }}
      enableReinitialize={true}
    >
      {({
        values,
        errors,
        touched,
        setFieldValue,
        setFieldError,
        handleBlur,
        handleChange,
        handleSubmit,
      }) => {
        // Check if user is super_admin and status is "open" - these fields should be optional
        const isSuperAdminAndOpen = isSuperAdmin && values?.status === 'open';

        // Check if ticket is assigned and status is "open" - these fields should be optional
        const isAssignedAndOpen =
          (values?.assignee || ticket?.assigned_to_user_id) &&
          values?.status === 'open';

        // Fields should be optional when super_admin with open status OR assigned with open status
        const shouldMakeOptional = isSuperAdminAndOpen || isAssignedAndOpen;

        return (
          <Form onSubmit={handleSubmit} className="form-wrap">
            {loading ? (
              <ContentLoader />
            ) : isOrgMaster ? (
              <TicketSummaryCard
                ticket={ticket}
                getDisplayValue={getDisplayValue}
                getTicketOwnerDisplayName={getTicketOwnerDisplayName}
                classificationOptions={classificationOptions}
                priorityOptions={priorityOptions}
                statusOptions={statusOptions}
              />
            ) : (
              <>
                {/* Key Information Section */}
                <Box className="form-section">
                  <Typography className="sub-content-text pt8 pb16">
                    Key Information
                  </Typography>

                  {/* Assignee Section */}
                  <Box className="field-group">
                    <Box className="field-row d-flex align-center gap-sm">
                      <Typography className="body-sm">Assignee</Typography>
                      <CustomUserSelect
                        className="assignee-select"
                        name="assignee"
                        placeholder={
                          loadingUsers
                            ? 'Loading users...'
                            : 'Select assignee...'
                        }
                        options={assigneeOptions}
                        value={values?.assignee}
                        onChange={(selectedOption) => {
                          setFieldValue('assignee', selectedOption);
                          // Clear assignee validation error when assignee is selected
                          if (selectedOption && errors?.assignee) {
                            setFieldError('assignee', '');
                          }
                        }}
                        isSearchable={true}
                        isClearable={true}
                        required={isSuperAdmin}
                        isDisabled={!canEditFields || loadingUsers}
                        error={Boolean(touched?.assignee && errors?.assignee)}
                        helperText={touched?.assignee && errors?.assignee}
                      />
                    </Box>
                  </Box>

                  {/* Ticket Owner Section */}
                  <Box className="field-group pt8">
                    <Typography className="sub-content-text pb16">
                      Ticket Owner
                    </Typography>
                    <Box className="info-wrap d-flex align-center gap-sm">
                      <HeaderImage
                        imageUrl={ticket?.creator_avatar_url}
                        type="avtar"
                        className="preview-img support-ticket-owner-img"
                        IsExternal={false}
                      />
                      <Typography className="profile-name body-sm text-capital">
                        {getTicketOwnerDisplayName()}
                      </Typography>
                    </Box>
                  </Box>

                  {/* Status and Date Section */}
                  <Box className="custom-select-wrap">
                    <Box>
                      <CustomSelect
                        label="Status"
                        name="status"
                        placeholder="Select Status"
                        options={statusOptions}
                        value={
                          statusOptions?.find(
                            (opt) => opt?.value === values?.status
                          ) || null
                        }
                        onChange={(selectedOption) => {
                          setFieldValue('status', selectedOption?.value || '');
                          // Clear validation errors when status changes from "open" to other statuses
                          if (
                            selectedOption?.value !== 'open' &&
                            errors?.status
                          ) {
                            setFieldError('status', '');
                          }
                        }}
                        error={Boolean(touched?.status && errors?.status)}
                        helperText={touched?.status && errors?.status}
                        required
                        isDisabled={!canEditFields && !isStaff}
                      />
                    </Box>
                    <Box>
                      <CustomDatePicker
                        label="Start Date ( DD/MM/YYYY )"
                        name="sdate"
                        value={values?.sdate ? dayjs(values?.sdate) : null}
                        onBlur={handleBlur}
                        onChange={(date) => {
                          setFieldValue('sdate', date);
                          // Clear error when date is filled
                          if (date && errors.sdate) {
                            setFieldError('sdate', '');
                          }
                        }}
                        disablePast
                        inputVariant="outlined"
                        format="DD/M/YYYY"
                        error={Boolean(
                          touched?.sdate && errors?.sdate && !shouldMakeOptional
                        )}
                        helperText={
                          touched?.sdate && errors?.sdate && !shouldMakeOptional
                            ? errors?.sdate
                            : ''
                        }
                        required={!shouldMakeOptional}
                        disabled={!canEdit && !isStaff}
                      />
                    </Box>
                    <Box>
                      <CustomDatePicker
                        label="Due Date ( DD/MM/YYYY )"
                        name="dueDate"
                        value={values?.dueDate ? dayjs(values?.dueDate) : null}
                        onBlur={handleBlur}
                        onChange={(date) => {
                          setFieldValue('dueDate', date);
                          // Clear error when date is filled
                          if (date && errors.dueDate) {
                            setFieldError('dueDate', '');
                          }
                        }}
                        inputVariant="outlined"
                        format="DD/M/YYYY"
                        error={Boolean(
                          touched?.dueDate &&
                            errors?.dueDate &&
                            !shouldMakeOptional
                        )}
                        helperText={
                          touched?.dueDate &&
                          errors?.dueDate &&
                          !shouldMakeOptional
                            ? errors?.dueDate
                            : ''
                        }
                        disabled={!canEdit && !isStaff}
                        minDate={
                          values?.sdate ? dayjs(values?.sdate) : undefined
                        }
                        required={!shouldMakeOptional}
                      />
                    </Box>
                  </Box>
                </Box>

                {/* Task Followers Section */}
                <Box className="form-section">
                  <Box className="followers-wrap d-flex pt8">
                    <Typography className="sub-content-text">
                      Task Followers
                    </Typography>
                    <Box className="followers-list-wrap gap-16">
                      <Box className="followers-list d-flex">
                        {taskFollowers?.map((follower, index) => {
                          return (
                            <Tooltip
                              title={
                                <Typography className="sub-title-text">
                                  {follower?.name}
                                </Typography>
                              }
                              classes={{
                                tooltip: 'info-tooltip-container ',
                              }}
                              arrow
                              key={index}
                            >
                              <Box className="follower-item d-flex align-center gap-8">
                                {follower?.image ? (
                                  <HeaderImage
                                    imageUrl={follower?.image}
                                    type="avtar"
                                    className="follower-img support-ticket-owner-img"
                                    IsExternal={false}
                                  />
                                ) : (
                                  <Avatar className="user-avatar-fallback follower-img support-ticket-owner-img">
                                    {follower?.name?.charAt(0)?.toUpperCase()}
                                  </Avatar>
                                )}

                                {canEdit && (
                                  <Box className="close-icon-wrap">
                                    <CloseIcon
                                      onClick={() => removeFollowers(index)}
                                      className="close-icon"
                                      sx={{
                                        cursor: 'pointer',
                                        '&:hover': {
                                          color: '#d32f2f',
                                        },
                                      }}
                                    />
                                  </Box>
                                )}
                              </Box>
                            </Tooltip>
                          );
                        })}
                      </Box>

                      {canEdit && (
                        <Box className="add-follower-section d-flex flex-col gap-sm align-start pt8">
                          <Box
                            className="add-follower-wrap d-flex align-center"
                            onClick={handleToggleClick}
                          >
                            <AddIcon className="add-btn" />
                            <Typography className="add-follow-text sub-content-text">
                              Add Followers
                            </Typography>
                          </Box>

                          {isAddFollowerOpen && (
                            <Box className="follower-select-container">
                              <CustomUserSelect
                                placeholder={
                                  loadingUsers
                                    ? 'Loading users...'
                                    : 'Search and select follower...'
                                }
                                className="followers-search"
                                options={getAvailableFollowers(values.assignee)}
                                value={null}
                                onChange={(selectedOption) => {
                                  if (selectedOption) {
                                    // Convert API user format to taskFollower format
                                    const newFollower = {
                                      name: selectedOption?.label,
                                      email: selectedOption?.email,
                                      id: selectedOption?.value,
                                      image: selectedOption?.avatar,
                                    };
                                    addFollower(newFollower);
                                    setIsAddFollowerOpen(false);
                                  }
                                }}
                                isSearchable={true}
                                isClearable={false}
                                menuPosition="absolute"
                                isDisabled={loadingUsers}
                              />
                            </Box>
                          )}
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>

                {/* Additional Information Section */}
                <Box className="form-section">
                  <Typography className="sub-content-text pt8 pb16">
                    Additional Information
                  </Typography>

                  <Box className="custom-select-wrap">
                    <Box>
                      <CustomSelect
                        label="Priority"
                        name="priority"
                        placeholder="Select priority"
                        options={priorityOptions}
                        value={
                          priorityOptions?.find(
                            (opt) => opt?.value === values?.priority
                          ) || null
                        }
                        onChange={(selectedOption) =>
                          setFieldValue('priority', selectedOption?.value || '')
                        }
                        error={Boolean(touched?.priority && errors?.priority)}
                        helperText={touched?.priority && errors?.priority}
                        required
                        isDisabled={!canEditFields}
                      />
                    </Box>
                    <Box>
                      <CustomSelect
                        label="Classifications"
                        name="classification"
                        placeholder="Select Classification"
                        options={classificationOptions}
                        value={
                          classificationOptions?.find(
                            (opt) => opt?.value === values?.classification
                          ) || null
                        }
                        onChange={(selectedOption) =>
                          setFieldValue(
                            'classification',
                            selectedOption?.value || ''
                          )
                        }
                        error={Boolean(
                          touched?.classification && errors?.classification
                        )}
                        helperText={
                          touched?.classification && errors?.classification
                        }
                        required
                        isDisabled={!canEdit}
                      />
                    </Box>
                    <Box>
                      <CustomTextField
                        fullWidth
                        label="Estimated Hours"
                        name="estimated_hours"
                        placeholder="Enter estimated hours"
                        type="number"
                        value={values?.estimated_hours}
                        onBlur={handleBlur}
                        onChange={(e) => {
                          handleChange(e);
                          // Clear error when value is filled
                          if (e.target.value && errors.estimated_hours) {
                            setFieldError('estimated_hours', '');
                          }
                        }}
                        error={Boolean(
                          touched?.estimated_hours &&
                            errors?.estimated_hours &&
                            !shouldMakeOptional
                        )}
                        helperText={
                          touched?.estimated_hours &&
                          errors?.estimated_hours &&
                          !shouldMakeOptional
                            ? errors?.estimated_hours
                            : ''
                        }
                        inputProps={{
                          min: 0,
                          max: 9999,
                          step: 0.5,
                        }}
                        required={!shouldMakeOptional}
                      />
                    </Box>
                  </Box>
                </Box>

                {/* Description Section */}
                <Box className="">
                  <CustomTextField
                    fullWidth
                    name="description"
                    label="Description"
                    placeholder="Enter description..."
                    value={values?.description}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    error={Boolean(touched?.description && errors?.description)}
                    helperText={touched?.description && errors?.description}
                    multiline
                    minRows={2}
                    maxRows={4}
                    required
                    disabled={!canEdit}
                  />
                </Box>

                {/* Action Buttons */}
                <Box className="form-section">
                  <Box className="d-flex justify-end pt16 gap-sm">
                    <CustomButton
                      type="button"
                      title="Cancel"
                      variant="outlined"
                      onClick={handleCancelClick}
                    />
                    {(canEdit || isStaff) && (
                      <CustomButton
                        type="submit"
                        title="Submit"
                        variant="contained"
                      />
                    )}
                  </Box>
                </Box>
              </>
            )}
          </Form>
        );
      }}
    </Formik>
  );
}
